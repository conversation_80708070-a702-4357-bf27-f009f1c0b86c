package com.sencha.tools.compiler.builder;

import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerContext;
import com.sencha.tools.compiler.ast.SourceBuilder;
import com.sencha.tools.compiler.sourcemap.SourceMapBuilder;
import com.sencha.tools.compiler.sources.SourceFile;
import com.sencha.util.FileUtil;
import com.sencha.util.StreamUtil;
import com.sencha.util.StringUtil;
import org.slf4j.Logger;

import java.io.Writer;

/**
 * Enhanced concatenator that generates source maps during file concatenation.
 */
public class SourceMapConcatenator extends SimpleConcatenator {
    
    private static final Logger _logger = SenchaLogManager.getLogger();
    
    private SourceMapBuilder _sourceMapBuilder;
    private String _outputFile;
    private boolean _generateSourceMap = false;
    private boolean _includeSourceContent = true;
    
    public SourceMapConcatenator(CompilerContext context, String outputFile) {
        super(context);
        _outputFile = outputFile;
    }
    
    public void setGenerateSourceMap(boolean generateSourceMap) {
        _generateSourceMap = generateSourceMap;
        if (_generateSourceMap) {
            _sourceMapBuilder = new SourceMapBuilder(_outputFile);
        }
    }
    
    public void setIncludeSourceContent(boolean includeSourceContent) {
        _includeSourceContent = includeSourceContent;
    }
    
    public void setSourceRoot(String sourceRoot) {
        if (_sourceMapBuilder != null) {
            _sourceMapBuilder.setSourceRoot(sourceRoot);
        }
    }
    
    public SourceMapBuilder getSourceMapBuilder() {
        return _sourceMapBuilder;
    }
    
    @Override
    public void doConcat(CompilerContext context, Writer writer) {
        if (_generateSourceMap) {
            _logger.info("concatenating files with source map generation...");
            concatFilesWithSourceMap(context, writer);
        } else {
            _logger.info("concatenating files...");
            super.doConcat(context, writer);
        }
    }
    
    private void concatFilesWithSourceMap(CompilerContext context, Writer writer) {
        StringBuilder builder = new StringBuilder();
        SourceBuilder cp = context.createSourceBuilder(getPretty(), getStripComments());
        
        // Configure source builder for source map generation
        cp.setGenerateSourceMap(true);
        cp.setSourceMapBuilder(_sourceMapBuilder);

        if (context != null && context.getBuildEnvironment() != null) {
            cp.setUnicodeEscapes(context.getBuildEnvironment().getUnicodeEscapes());
        }
        
        for (SourceFile sf : context.getEnabledSourceFiles()) {
            String sourceFilePath = sf.getCanonicalPath();
            _logger.debug("Processing source file: {}", sourceFilePath);
            
            // Set current source file for mapping
            cp.setCurrentSourceFile(sourceFilePath);
            
            // Add source content to source map if requested
            if (_includeSourceContent) {
                try {
                    String sourceContent = sf.getPreprocessedSource();
                    _sourceMapBuilder.addSourceContent(sourceFilePath, sourceContent);
                } catch (Exception e) {
                    _logger.warn("Could not read source content for {}: {}", sourceFilePath, e.getMessage());
                }
            }
            
            // Generate output with source mapping
            cp.print(sf.getAstRoot(), builder);
            builder.append(StringUtil.NewLine);
            
            // Update source map position for newline
            _sourceMapBuilder.incrementGeneratedLine();
            
            String data = builder.toString();
            builder.setLength(0);

            if (context != null) {
                data = context.removeFilePrefix(data);
            }

            StreamUtil.writeData(writer, data);
        }
    }
    
    /**
     * Generate the source map content as JSON string
     */
    public String generateSourceMapJson() {
        if (_sourceMapBuilder == null) {
            return null;
        }
        return _sourceMapBuilder.toJson();
    }
    
    /**
     * Write the source map to a file
     */
    public void writeSourceMapFile(String sourceMapPath) {
        String sourceMapJson = generateSourceMapJson();
        if (sourceMapJson != null) {
            FileUtil.writeFile(sourceMapPath, sourceMapJson);
            _logger.info("Source map written to: {}", sourceMapPath);
        }
    }
    
    /**
     * Get the source map URL comment to append to the generated file
     */
    public String getSourceMapUrlComment(String sourceMapFileName) {
        return "\n//# sourceMappingURL=" + sourceMapFileName;
    }
}
