package com.sencha.tools.compiler.sourcemap;

import com.sencha.util.StringUtil;
import java.util.*;

/**
 * Builder for generating source maps during JavaScript compilation and concatenation.
 * Tracks mappings between original source files and generated output.
 */
public class SourceMapBuilder {
    
    private static final int VLQ_BASE_SHIFT = 5;
    private static final int VLQ_BASE = 1 << VLQ_BASE_SHIFT;
    private static final int VLQ_BASE_MASK = VLQ_BASE - 1;
    private static final int VLQ_CONTINUATION_BIT = VLQ_BASE;
    
    private List<SourceMapping> _mappings = new ArrayList<>();
    private Set<String> _sources = new LinkedHashSet<>();
    private Set<String> _names = new LinkedHashSet<>();
    private Map<String, String> _sourceContents = new HashMap<>();
    
    private int _currentGeneratedLine = 1;
    private int _currentGeneratedColumn = 0;
    private String _outputFile;
    private String _sourceRoot;
    
    public SourceMapBuilder(String outputFile) {
        _outputFile = outputFile;
    }
    
    public void setSourceRoot(String sourceRoot) {
        _sourceRoot = sourceRoot;
    }
    
    public void addMapping(String sourceFile, int originalLine, int originalColumn,
                          int generatedLine, int generatedColumn, String name) {
        if (sourceFile != null) {
            _sources.add(sourceFile);
        }
        if (name != null) {
            _names.add(name);
        }
        
        SourceMapping mapping = new SourceMapping();
        mapping.sourceFile = sourceFile;
        mapping.originalLine = originalLine;
        mapping.originalColumn = originalColumn;
        mapping.generatedLine = generatedLine;
        mapping.generatedColumn = generatedColumn;
        mapping.name = name;
        
        _mappings.add(mapping);
    }
    
    public void addSourceContent(String sourceFile, String content) {
        _sourceContents.put(sourceFile, content);
    }
    
    public int getCurrentGeneratedLine() {
        return _currentGeneratedLine;
    }
    
    public int getCurrentGeneratedColumn() {
        return _currentGeneratedColumn;
    }
    
    public void incrementGeneratedLine() {
        _currentGeneratedLine++;
        _currentGeneratedColumn = 0;
    }
    
    public void incrementGeneratedColumn() {
        _currentGeneratedColumn++;
    }
    
    public void setGeneratedPosition(int line, int column) {
        _currentGeneratedLine = line;
        _currentGeneratedColumn = column;
    }
    
    /**
     * Generate the source map JSON string
     */
    public String toJson() {
        StringBuilder json = new StringBuilder();
        json.append("{\n");
        json.append("\"version\":3,\n");
        json.append("\"file\":\"").append(escapeJson(_outputFile)).append("\",\n");
        
        if (_sourceRoot != null) {
            json.append("\"sourceRoot\":\"").append(escapeJson(_sourceRoot)).append("\",\n");
        }
        
        // Sources array
        json.append("\"sources\":[");
        boolean first = true;
        for (String source : _sources) {
            if (!first) json.append(",");
            json.append("\"").append(escapeJson(source)).append("\"");
            first = false;
        }
        json.append("],\n");
        
        // Names array
        json.append("\"names\":[");
        first = true;
        for (String name : _names) {
            if (!first) json.append(",");
            json.append("\"").append(escapeJson(name)).append("\"");
            first = false;
        }
        json.append("],\n");
        
        // Source contents (optional)
        if (!_sourceContents.isEmpty()) {
            json.append("\"sourcesContent\":[");
            first = true;
            for (String source : _sources) {
                if (!first) json.append(",");
                String content = _sourceContents.get(source);
                if (content != null) {
                    json.append("\"").append(escapeJson(content)).append("\"");
                } else {
                    json.append("null");
                }
                first = false;
            }
            json.append("],\n");
        }
        
        // Mappings
        json.append("\"mappings\":\"").append(generateMappings()).append("\"\n");
        json.append("}");
        
        return json.toString();
    }
    
    private String generateMappings() {
        // Sort mappings by generated position
        _mappings.sort((a, b) -> {
            int lineDiff = Integer.compare(a.generatedLine, b.generatedLine);
            if (lineDiff != 0) return lineDiff;
            return Integer.compare(a.generatedColumn, b.generatedColumn);
        });
        
        StringBuilder mappings = new StringBuilder();
        int previousGeneratedLine = 1;
        int previousGeneratedColumn = 0;
        int previousSourceIndex = 0;
        int previousOriginalLine = 0;
        int previousOriginalColumn = 0;
        int previousNameIndex = 0;
        
        List<String> sourcesList = new ArrayList<>(_sources);
        List<String> namesList = new ArrayList<>(_names);
        
        for (SourceMapping mapping : _mappings) {
            // Add semicolons for new lines
            while (previousGeneratedLine < mapping.generatedLine) {
                mappings.append(";");
                previousGeneratedLine++;
                previousGeneratedColumn = 0;
            }
            
            if (mappings.length() > 0 && !mappings.toString().endsWith(";")) {
                mappings.append(",");
            }
            
            // Generated column
            mappings.append(encodeVLQ(mapping.generatedColumn - previousGeneratedColumn));
            previousGeneratedColumn = mapping.generatedColumn;
            
            if (mapping.sourceFile != null) {
                // Source file index
                int sourceIndex = sourcesList.indexOf(mapping.sourceFile);
                mappings.append(encodeVLQ(sourceIndex - previousSourceIndex));
                previousSourceIndex = sourceIndex;
                
                // Original line
                mappings.append(encodeVLQ(mapping.originalLine - 1 - previousOriginalLine));
                previousOriginalLine = mapping.originalLine - 1;
                
                // Original column
                mappings.append(encodeVLQ(mapping.originalColumn - previousOriginalColumn));
                previousOriginalColumn = mapping.originalColumn;
                
                // Name index (optional)
                if (mapping.name != null) {
                    int nameIndex = namesList.indexOf(mapping.name);
                    mappings.append(encodeVLQ(nameIndex - previousNameIndex));
                    previousNameIndex = nameIndex;
                }
            }
        }
        
        return mappings.toString();
    }
    
    private String encodeVLQ(int value) {
        StringBuilder encoded = new StringBuilder();
        
        // Convert to unsigned and add sign bit
        int vlq = value < 0 ? ((-value) << 1) | 1 : value << 1;
        
        do {
            int digit = vlq & VLQ_BASE_MASK;
            vlq >>>= VLQ_BASE_SHIFT;
            
            if (vlq > 0) {
                digit |= VLQ_CONTINUATION_BIT;
            }
            
            encoded.append(base64Encode(digit));
        } while (vlq > 0);
        
        return encoded.toString();
    }
    
    private char base64Encode(int value) {
        if (value < 26) return (char) ('A' + value);
        if (value < 52) return (char) ('a' + value - 26);
        if (value < 62) return (char) ('0' + value - 52);
        if (value == 62) return '+';
        if (value == 63) return '/';
        throw new IllegalArgumentException("Invalid base64 value: " + value);
    }
    
    private String escapeJson(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
    
    private static class SourceMapping {
        String sourceFile;
        int originalLine;
        int originalColumn;
        int generatedLine;
        int generatedColumn;
        String name;
    }
}
