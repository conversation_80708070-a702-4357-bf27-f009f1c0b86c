/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compressors.es6;

import com.sencha.command.environment.BuildEnvironment;
import com.sencha.exceptions.BasicException;
import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.SourceBuilder;
import com.sencha.tools.compiler.ast.js.RootNode;
import com.sencha.tools.compiler.sourcemap.SourceMapBuilder;
import com.sencha.tools.compressors.BaseCompressor;
import com.sencha.tools.compressors.Compressor;
import com.sencha.tools.compressors.JsLanguageLevel;
import com.sencha.tools.compressors.closure.ClosureCompressor;
import com.sencha.util.Converter;
import com.sencha.util.ObjectUtil;
import com.sencha.util.StringBuilderWriter;

import java.io.IOException;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CmdJavascriptCompressor extends BaseCompressor {

    private VariableRenamer _renamer = new VariableRenamer();
    private BooleanCompressor _booleanCompressor = new BooleanCompressor();

    private List<CompressorTransform> _transforms = new ArrayList<CompressorTransform>(){{
        add(_renamer);
        add(_booleanCompressor);
    }};

    // Sourcemap support
    private SourceMapBuilder _sourceMapBuilder;
    private String _sourceMapJson;

    @Override
    public String compress(String data) {
        StringBuilderWriter writer = new StringBuilderWriter();
        compress(data, writer);
        return writer.getBuilder().toString();
    }

    @Override
    public void compress(String data, Writer writer) {
        String renameFnKey = "renameFunctions";
        Map<String, Object> options = getOptions();
        if (options.containsKey(renameFnKey)) {
            boolean preserve = !Converter.convert(options.get(renameFnKey), Boolean.class);
            _renamer.setKeepFunctionNames(preserve);
            options.remove(renameFnKey);
        }

        data = doJsTranspile(data);

        RootNode node = AstUtil.parse(data);
        for (CompressorTransform transform: _transforms) {
            transform.compress(node);
        }
        SourceBuilder sb = new SourceBuilder();
        sb.setPretty(false);
        sb.setStripComments(true);
        if (options.containsKey("wrapLines")) {
            sb.setWrapLines(Converter.convert(options.get("wrapLines"), Integer.class));
        }

        // Configure sourcemap if enabled
        if (options.containsKey("generateSourceMap") &&
            Converter.convert(options.get("generateSourceMap"), Boolean.class)) {
            String outputFile = options.containsKey("outputFile") ?
                (String) options.get("outputFile") : "output.js";
            _sourceMapBuilder = new SourceMapBuilder(outputFile);

            if (options.containsKey("sourceRoot")) {
                _sourceMapBuilder.setSourceRoot((String) options.get("sourceRoot"));
            }

            sb.setGenerateSourceMap(true);
            sb.setSourceMapBuilder(_sourceMapBuilder);

            String sourceFile = options.containsKey("sourceFile") ?
                (String) options.get("sourceFile") : "input.js";
            sb.setCurrentSourceFile(sourceFile);

            // Add source content if available
            if (options.containsKey("includeSourceContent") &&
                Converter.convert(options.get("includeSourceContent"), Boolean.class)) {
                _sourceMapBuilder.addSourceContent(sourceFile, data);
            }
        }

        if (_buildEnvironment != null) {
            sb.setUnicodeEscapes(_buildEnvironment.getUnicodeEscapes());
        }
        try {
            String output = sb.print(node);
            writer.write(output);

            // Generate source map JSON if enabled
            if (_sourceMapBuilder != null) {
                _sourceMapJson = _sourceMapBuilder.toJson();
            }
        } catch (IOException e) {
            throw BasicException.raise(e);
        }
    }

    /**
     * Get the generated source map JSON string
     */
    public String getSourceMap() {
        String result = _sourceMapJson;
        _sourceMapJson = null; // Clear after retrieval
        return result;
    }

}
