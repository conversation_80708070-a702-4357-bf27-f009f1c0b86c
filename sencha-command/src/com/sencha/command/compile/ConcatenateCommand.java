/*******************************************************************************
 * Copyright (c) 2012-2013. Sencha Inc.
 ******************************************************************************/

package com.sencha.command.compile;

import com.sencha.cli.annotations.Configures;
import com.sencha.cli.annotations.Doc;
import com.sencha.cli.annotations.Private;
import com.sencha.exceptions.ExBuild;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerContext;
import com.sencha.tools.compiler.builder.NamespaceBuilder;
import com.sencha.tools.compiler.builder.SimpleConcatenator;
import com.sencha.tools.compiler.builder.SourceMapConcatenator;
import com.sencha.tools.compressors.es6.CmdJavascriptCompressor;
import com.sencha.tools.compressors.closure.ClosureCompressor;

import java.io.File;
import java.util.HashMap;
import com.sencha.tools.compiler.builder.metadata.AliasesGenerator;
import com.sencha.tools.compiler.builder.metadata.AlternateNamesGenerator;
import com.sencha.tools.compiler.builder.metadata.BaseMetadataGenerator;
import com.sencha.tools.compiler.builder.optimizer.ReferenceOptimizerBuilder;
import com.sencha.tools.compressors.Compressor;
import com.sencha.tools.compressors.JsLanguageLevel;
import com.sencha.tools.compressors.closure.ClosureCompressor;
import com.sencha.tools.compressors.es6.CmdJavascriptCompressor;
import com.sencha.tools.compressors.uglify.UglifyCompressor;
import com.sencha.tools.generator.XTemplateTransformer;
import com.sencha.tools.page.AppJsonBuilder;
import com.sencha.tools.page.PageModel;
import com.sencha.util.FileUtil;
import com.sencha.util.PathUtil;
import com.sencha.util.StringBuilderWriter;
import com.sencha.util.StringUtil;
import org.slf4j.Logger;

import java.io.File;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

import static com.sencha.util.StringUtil.isNullOrEmpty;

@Doc("Produce output file by concatenating the files in the current set")
public class ConcatenateCommand extends BaseOutputCommand {
    private static final Logger _logger = SenchaLogManager.getLogger();

    private boolean _stripComments = false;
    private boolean _compress = false;
    private boolean _includeNamespaceStatements = false;
    private boolean _includeNamespace = false;
    private boolean _includeMetadataBlock = false;
    private Compressor _compressor;
    private boolean _sandbox;
    private String _sandboxJsPrefix;
    private String _sandboxCssPrefix;
    private boolean _beautify;
    private boolean _removeTextReferences = true;
    private boolean _removeRequirementNodes = true;
    private boolean _optimizeStringReferences = true;
    private JsLanguageLevel _outJsVersion = null;
    private JsLanguageLevel _inJsVersion = null;
    private String _charset = null;
    private boolean _generateSourceMap = false;
    private boolean _includeSourceContent = true;
    private String _sourceRoot;

    @Doc("Compress generated file using default compressor (cmd)")
    public void setCompress (boolean enable) {
        _compress = enable;
        if (enable) {
            _compressor = new CmdJavascriptCompressor();
        } else {
            _compressor = null;
        }
    }

    public boolean getCompress () {
        return _compress;
    }

    @Doc("Compress generated file using Cmd compressor (the default).")
    public void setCmd(boolean enable) {
        _compress = enable;
        if (enable) {
            _compressor = new CmdJavascriptCompressor();
        } else {
            _compressor = null;
        }
    }
    
    public boolean getCmd() {
        return _compress && _compressor != null && _compressor.getClass().equals(
                CmdJavascriptCompressor.class);
    }
    
    @Private
    @Doc("YUI compressor has been removed (using --cmd instead).")
    public void setYui (boolean enable) {
        setCmd(enable);
    }

    public boolean getYui () {
        return getCmd();
    }

    @Private
    @Doc("Compress generate file using Closure Compiler (experimental / not recommended).")
    public void setClosure (boolean enable) {
        _compress = enable;
        if (enable) {
            _compressor = new ClosureCompressor();
        } else {
            _compressor = null;
        }
    }

    public boolean getClosure () {
        return _compress && _compressor != null && _compressor.getClass().equals(
            ClosureCompressor.class);
    }

    @Private
    @Doc("Compress generate file using uglify-js (experimental / not recommended).")
    public void setUglify (boolean enable) {
        _compress = enable;
        if (enable) {
            _compressor = new UglifyCompressor();
        } else {
            _compressor = null;
        }
    }

    public boolean getUglify () {
        return _compress && _compressor != null && _compressor.getClass().equals(
            UglifyCompressor.class);
    }


    @Doc("Strip comments from the generated file")
    public void setStripComments (boolean enable) {
        _stripComments = enable;
    }

    @Doc("enables / disables reference optimization by converting string classnames to static references")
    public void setRemoveTextReferences (boolean enable) {
        _removeTextReferences = enable;
    }

    public boolean getRemoveTextReferences () {
        return _removeTextReferences;
    }
    
    public void setRemoveRequirementNodes(boolean enable) {
        _removeRequirementNodes = enable;
        if(enable) {
            _removeTextReferences = true;
        } else if(!_optimizeStringReferences) {
            _removeTextReferences = false;
        }
    }
    
    public boolean getRemoveRequirementNodes() {
        return _removeRequirementNodes;
    }
    
    public void setOptimizeStringReferences(boolean enable) {
        _optimizeStringReferences = enable;
        if(enable) {
            _removeTextReferences = true;
        } else if(!_removeRequirementNodes) {
            _removeTextReferences = false;
        }
    }

    public boolean getOptimizeStringReferences() {
        return _optimizeStringReferences;
    }

    @Doc("enables / disables beautification of sources after compilation")
    public void setBeautify (boolean enable) {
        _beautify = enable;
        _compress = !_beautify;
    }

    public boolean getBeautify () {
        return _beautify;
    }

    //@Doc("Specifies the sandbox prefixes for concatenation in the form <JsNamespace>:<CssPrefix>")
    public void setSandbox (String prefixes) {
        _sandbox = true;
        String[] parts = prefixes.split(":");
        _sandboxJsPrefix = parts[0];
        _sandboxCssPrefix = parts[1];
    }

    public void setMetadataBlock (boolean enable) {
        _includeMetadataBlock = enable;
    }

    ConcatenateCommand (CompileCommands compileCommands) {
        super(compileCommands);
    }

    public void execute (@Configures("output-file") String outPath) {

        // required for direct calls not going through dispatch layer
        // (the property setter will never be invoked otherwise)
        if (!isNullOrEmpty(outPath)) {
            setOutputFile(outPath);
        }

        CompilerContext context = getCompileCommands().getContext();

        StringBuilderWriter writer = new StringBuilderWriter();
        NamespaceBuilder nsBuilder = new NamespaceBuilder();

        // Use SourceMapConcatenator if source map generation is enabled
        SimpleConcatenator concat;
        SourceMapConcatenator sourceMapConcat = null;
        if (_generateSourceMap) {
            sourceMapConcat = new SourceMapConcatenator(context, getOutputFile());
            sourceMapConcat.setGenerateSourceMap(true);
            sourceMapConcat.setIncludeSourceContent(_includeSourceContent);
            if (_sourceRoot != null) {
                sourceMapConcat.setSourceRoot(_sourceRoot);
            }
            concat = sourceMapConcat;
        } else {
            concat = new SimpleConcatenator(context);
        }

        if (context.includeNamespace()) {
            nsBuilder.doConcat(context, writer);
        }

        if (_includeMetadataBlock) {
            BaseMetadataGenerator metaGen;

            metaGen = new AliasesGenerator();
            metaGen.setTpl("Ext._aliasMetadata = {0};\n");
            metaGen.doConcat(context, writer);

            metaGen = new AlternateNamesGenerator();
            metaGen.setTpl("Ext._alternatesMetadata = {0};");
            metaGen.doConcat(context, writer);
        }

        if (_removeTextReferences) {
            final boolean removeNodes = getRemoveRequirementNodes();
            final boolean unquoteNodes = getOptimizeStringReferences();
            ReferenceOptimizerBuilder opt = new ReferenceOptimizerBuilder(){{
                setRemoveRequirementNodes(removeNodes);
                setOptimizeStringReferences(unquoteNodes);
            }};
            opt.build(context);
        }

        concat.setPretty(true);
        concat.setStripComments(_stripComments);

        concat.doConcat(context, writer);
        String data = writer.getBuilder().toString();
        writer.resetBuilder();

        JsLanguageLevel outLevel = getBuildEnvironment().getOutputLanguageLevel();
        JsLanguageLevel inLevel = getBuildEnvironment().getInputLanguageLevel();
        
        if (_outJsVersion != null) {
            outLevel = _outJsVersion;
        }
        
        if (_inJsVersion != null) {
            inLevel = _inJsVersion; 
        }
        
        boolean cleanUpOutput = false;
        
        if (inLevel.shouldTranspile(outLevel)) {
            if (_compressor == null) {
                _compress = true;
                ClosureCompressor comp = new ClosureCompressor();
                comp.setTranspileOnly(true);
                _compressor = comp;
                cleanUpOutput = true;
            }
        }
        
        if (_charset == null) {
            _charset = getBuildEnvironment().getOutputCharset();
        }
        
        // reload tmp file and compress content, saving result back to tmp file
        if (_compress && _compressor != null) {
            _logger.info("Processing data with {}",
                _compressor.getClass().getSimpleName());

            AppJsonBuilder builder = context.getAppJsonBuilder();
            Map<String, Object> compressorOpts = new HashMap<>();
            if(builder != null) {
                PageModel model = context.getAppJsonBuilder().getPageModel();
                if(model != null) {
                    Map<String, Object> modelOpts = model.getCompressorOptions();
                    if(modelOpts != null) {
                        compressorOpts.putAll(modelOpts);
                        if (modelOpts.containsKey("outputCharset")) {
                            if (_charset == null) {
                                _charset = (String) modelOpts.get("outputCharset");
                            }

                        }
                        if (modelOpts.containsKey("charset")) {
                            if (_charset == null) {
                                _charset = (String) modelOpts.get("charset");
                            }
                        }
                    }
                }
            }

            // Add sourcemap options if enabled
            if (_generateSourceMap) {
                compressorOpts.put("generateSourceMap", true);
                compressorOpts.put("outputFile", getOutputFile());
                compressorOpts.put("includeSourceContent", _includeSourceContent);
                if (_sourceRoot != null) {
                    compressorOpts.put("sourceRoot", _sourceRoot);
                }
            }

            _compressor.setOptions(compressorOpts);

            if (outLevel != null) {
                _compressor.setOutputLanguageLevel(outLevel);
            }

            if (inLevel != null) {
                _compressor.setInputLanguageLevel(inLevel);
            }
            
            _compressor.setBuildEnvironment(getBuildEnvironment());
            data = _compressor.compress(data);

            // Handle compressor source map if generated
            if (_generateSourceMap && _compressor instanceof CmdJavascriptCompressor) {
                CmdJavascriptCompressor cmdCompressor = (CmdJavascriptCompressor) _compressor;
                String compressorSourceMap = cmdCompressor.getSourceMap();
                if (compressorSourceMap != null) {
                    String sourceMapFileName = PathUtil.getFileName(getOutputFile()) + ".map";
                    String sourceMapPath = PathUtil.getParent(getOutputFile()) + File.separator + sourceMapFileName;
                    FileUtil.writeFile(sourceMapPath, compressorSourceMap);
                    _logger.info("Compressor source map written to: {}", sourceMapPath);

                    // Add source map URL comment to the output
                    data += "\n//# sourceMappingURL=" + sourceMapFileName;
                }
            } else if (_generateSourceMap && _compressor instanceof ClosureCompressor) {
                ClosureCompressor closureCompressor = (ClosureCompressor) _compressor;
                String compressorSourceMap = closureCompressor.getSourceMap();
                if (compressorSourceMap != null) {
                    String sourceMapFileName = PathUtil.getFileName(getOutputFile()) + ".map";
                    String sourceMapPath = PathUtil.getParent(getOutputFile()) + File.separator + sourceMapFileName;
                    FileUtil.writeFile(sourceMapPath, compressorSourceMap);
                    _logger.info("Closure compressor source map written to: {}", sourceMapPath);

                    // Add source map URL comment to the output
                    data += "\n//# sourceMappingURL=" + sourceMapFileName;
                }
            }

//            if (cleanUpOutput) {
//                data = AstUtil.toSource(AstUtil.parse(data), true);
//            }
        }

        // reload tmpfile content and apply sandbox template
        if (_sandbox) {
            data = evaluateSandbox(data);
        }

        boolean append = isAppend();
        String outFile = getOutputFile();
        outFile = PathUtil.normalizePath(outFile);
        outFile = PathUtil.getCanonicalPath(outFile);
        outFile = PathUtil.removeDuplicatePathSeparators(outFile);
        PathUtil.ensurePathExists(outFile);
        
        String prefix = append ? "Appending" : "Writing";
        _logger.info("{} concatenated output to file {}", prefix, outFile);

        // Generate source map if enabled and not already handled by compressor
        if (_generateSourceMap && sourceMapConcat != null && !_compress) {
            String sourceMapFileName = PathUtil.getFileName(outFile) + ".map";
            String sourceMapPath = PathUtil.getParent(outFile) + File.separator + sourceMapFileName;

            // Add source map URL comment to the output
            data += sourceMapConcat.getSourceMapUrlComment(sourceMapFileName);

            // Write source map file
            sourceMapConcat.writeSourceMapFile(sourceMapPath);
        }

        if (StringUtil.isNullOrEmpty(_charset)) {
            if (isAppend()) {
                FileUtil.appendFile(outFile, data);
            }
            else {
                FileUtil.writeFile(outFile, data);
            }
        }
        else {
            _logger.info("Using charset '{}' for file {}", _charset, outFile);
            data = "//@charset " + _charset + StringUtil.NewLine + data;
            byte[] bytes = data.getBytes(Charset.forName(_charset));
            FileUtil.writeFileData(new File(outFile), bytes, FileUtil.PathHandling.CREATE_IF_NECESSARY, append);
        }
    }

    private String evaluateSandbox (String data) {
        String tpl = FileUtil.readFile(getSandboxTemplate());
        Map<String, Object> context = new HashMap<String, Object>();
        context.put("cssPrefix", _sandboxCssPrefix);
        context.put("jsPrefix", _sandboxJsPrefix);
        context.put("data", data);
        XTemplateTransformer tx = new XTemplateTransformer();
        return tx.transform(tpl, context);
    }

    private String getSandboxTemplate () {
        String filename = "sandbox-template.js.tpl";

        _logger.trace("resolving {}", filename);
        String configPath = getCompileCommands().getFrameworkConfigPath();
        File file = new File(configPath, filename);
        _logger.debug("checking for sandbox template at {}", file.getAbsolutePath());
        if (file.exists()) {
            return PathUtil.getAbsolutePath(file);
        }

        throw new ExBuild("Failed to locate sandbox template file {0}", filename);
    }

    public JsLanguageLevel getJsVersion() {
        return _outJsVersion;
    }

    @Doc("Sets the output JS language level")
    public void setJsVersion(JsLanguageLevel jsVersion) {
        _outJsVersion = jsVersion;
    }

    public JsLanguageLevel getInputJsVersion() {
        return _inJsVersion;
    }

    @Doc("Sets the input JS language level")
    public void setInputJsVersion(JsLanguageLevel jsVersion) {
        _inJsVersion = jsVersion;
    }

    public String getCharset() {
        return _charset;
    }
    
    @Doc("specifies a custom charset for this output command.")
    public void setCharset(String charset) {
        _charset = charset;
    }

    @Doc("Generate source map file for debugging")
    public void setSourceMap(boolean generateSourceMap) {
        _generateSourceMap = generateSourceMap;
    }

    public boolean getSourceMap() {
        return _generateSourceMap;
    }

    @Doc("Include source content in source map")
    public void setSourceMapIncludeContent(boolean includeSourceContent) {
        _includeSourceContent = includeSourceContent;
    }

    public boolean getSourceMapIncludeContent() {
        return _includeSourceContent;
    }

    @Doc("Source root path for source map")
    public void setSourceRoot(String sourceRoot) {
        _sourceRoot = sourceRoot;
    }

    public String getSourceRoot() {
        return _sourceRoot;
    }
}
